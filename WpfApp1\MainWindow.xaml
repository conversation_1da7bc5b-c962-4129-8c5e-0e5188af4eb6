<Window x:Class="WpfApp1.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:WpfApp1"
        mc:Ignorable="d"
        Title="MainWindow" Height="450" Width="800">
    <Grid>
        <StackPanel>
            <TextBox Text="{Binding UserName}" HorizontalContentAlignment="Center" VerticalContentAlignment="Center" FontSize="23" Width="200" Height="50"></TextBox>
            <Button Content="确定" Command="{Binding GetNameCommand}" Width="200" Height="50"/>
        </StackPanel>
    </Grid>
</Window>
