﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WpfApp1.VM
{
    public partial class MainWindowVM : ObservableObject
    {
        [ObservableProperty]
        private string userName;

        [RelayCommand]
        private void GetName() 
        {
            userName = "你好";
        }
    }
}
