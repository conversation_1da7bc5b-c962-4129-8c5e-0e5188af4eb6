{"version": 3, "targets": {"net8.0-windows7.0": {"CommunityToolkit.Mvvm/8.4.0": {"type": "package", "compile": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/CommunityToolkit.Mvvm.targets": {}}}, "HandyControl/3.6.0-rc2": {"type": "package", "compile": {"lib/net8.0/HandyControl.dll": {"related": ".deps.json;.xml"}}, "runtime": {"lib/net8.0/HandyControl.dll": {"related": ".deps.json;.xml"}}}}}, "libraries": {"CommunityToolkit.Mvvm/8.4.0": {"sha512": "tqVU8yc/ADO9oiTRyTnwhFN68hCwvkliMierptWOudIAvWY1mWCh5VFh+guwHJmpMwfg0J0rY+yyd5Oy7ty9Uw==", "type": "package", "path": "communitytoolkit.mvvm/8.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "License.md", "ThirdPartyNotices.txt", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.12/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.12/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "build/CommunityToolkit.Mvvm.FeatureSwitches.targets", "build/CommunityToolkit.Mvvm.SourceGenerators.targets", "build/CommunityToolkit.Mvvm.Windows.targets", "build/CommunityToolkit.Mvvm.WindowsSdk.targets", "build/CommunityToolkit.Mvvm.targets", "buildTransitive/CommunityToolkit.Mvvm.FeatureSwitches.targets", "buildTransitive/CommunityToolkit.Mvvm.SourceGenerators.targets", "buildTransitive/CommunityToolkit.Mvvm.Windows.targets", "buildTransitive/CommunityToolkit.Mvvm.WindowsSdk.targets", "buildTransitive/CommunityToolkit.Mvvm.targets", "communitytoolkit.mvvm.8.4.0.nupkg.sha512", "communitytoolkit.mvvm.nuspec", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.dll", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.pdb", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.xml", "lib/net8.0/CommunityToolkit.Mvvm.dll", "lib/net8.0/CommunityToolkit.Mvvm.pdb", "lib/net8.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.0/CommunityToolkit.Mvvm.dll", "lib/netstandard2.0/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.1/CommunityToolkit.Mvvm.dll", "lib/netstandard2.1/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.1/CommunityToolkit.Mvvm.xml"]}, "HandyControl/3.6.0-rc2": {"sha512": "3J7+3c7SJwG3NpyDI1GhRirp0xUqJhbNl5lbn2SerxdNXSEAEx85m6rK9v+1pObElr/jAEAgk8+SzYHtHIdzow==", "type": "package", "path": "handycontrol/3.6.0-rc2", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "handycontrol.3.6.0-rc2.nupkg.sha512", "handycontrol.nuspec", "icon.png", "lib/net40/HandyControl.dll", "lib/net40/HandyControl.xml", "lib/net45/HandyControl.dll", "lib/net45/HandyControl.xml", "lib/net451/HandyControl.dll", "lib/net451/HandyControl.xml", "lib/net452/HandyControl.dll", "lib/net452/HandyControl.xml", "lib/net46/HandyControl.dll", "lib/net46/HandyControl.xml", "lib/net461/HandyControl.dll", "lib/net461/HandyControl.xml", "lib/net462/HandyControl.dll", "lib/net462/HandyControl.xml", "lib/net47/HandyControl.dll", "lib/net47/HandyControl.xml", "lib/net471/HandyControl.dll", "lib/net471/HandyControl.xml", "lib/net472/HandyControl.dll", "lib/net472/HandyControl.xml", "lib/net48/HandyControl.dll", "lib/net48/HandyControl.xml", "lib/net481/HandyControl.dll", "lib/net481/HandyControl.xml", "lib/net5.0/HandyControl.deps.json", "lib/net5.0/HandyControl.dll", "lib/net5.0/HandyControl.xml", "lib/net6.0/HandyControl.deps.json", "lib/net6.0/HandyControl.dll", "lib/net6.0/HandyControl.xml", "lib/net7.0/HandyControl.deps.json", "lib/net7.0/HandyControl.dll", "lib/net7.0/HandyControl.xml", "lib/net8.0/HandyControl.deps.json", "lib/net8.0/HandyControl.dll", "lib/net8.0/HandyControl.xml", "lib/net9.0/HandyControl.deps.json", "lib/net9.0/HandyControl.dll", "lib/net9.0/HandyControl.xml", "lib/netcoreapp3.0/HandyControl.deps.json", "lib/netcoreapp3.0/HandyControl.dll", "lib/netcoreapp3.0/HandyControl.xml", "lib/netcoreapp3.1/HandyControl.deps.json", "lib/netcoreapp3.1/HandyControl.dll", "lib/netcoreapp3.1/HandyControl.xml", "tools/VisualStudioToolsManifest.xml"]}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["CommunityToolkit.Mvvm >= 8.4.0", "HandyControl >= 3.6.0-rc2"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\2025\\程序\\欣旺达电池检测\\WpfTest\\WpfApp1\\WpfApp1\\WpfApp1.csproj", "projectName": "WpfApp1", "projectPath": "D:\\2025\\程序\\欣旺达电池检测\\WpfTest\\WpfApp1\\WpfApp1\\WpfApp1.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\2025\\程序\\欣旺达电池检测\\WpfTest\\WpfApp1\\WpfApp1\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://218.201.129.20:8111/nuget": {}, "https://api.nuget.org/v3/index.json": {}, "https://wlab.top:8111/nuget": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "HandyControl": {"target": "Package", "version": "[3.6.0-rc2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}}