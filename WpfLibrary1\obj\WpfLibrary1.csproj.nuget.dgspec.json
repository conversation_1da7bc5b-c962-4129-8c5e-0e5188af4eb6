{"format": 1, "restore": {"D:\\2025\\程序\\欣旺达电池检测\\WpfTest\\WpfApp1\\WpfLibrary1\\WpfLibrary1.csproj": {}}, "projects": {"D:\\2025\\程序\\欣旺达电池检测\\WpfTest\\WpfApp1\\WpfLibrary1\\WpfLibrary1.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\2025\\程序\\欣旺达电池检测\\WpfTest\\WpfApp1\\WpfLibrary1\\WpfLibrary1.csproj", "projectName": "WpfLibrary1", "projectPath": "D:\\2025\\程序\\欣旺达电池检测\\WpfTest\\WpfApp1\\WpfLibrary1\\WpfLibrary1.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\2025\\程序\\欣旺达电池检测\\WpfTest\\WpfApp1\\WpfLibrary1\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://218.201.129.20:8111/nuget": {}, "https://api.nuget.org/v3/index.json": {}, "https://wlab.top:8111/nuget": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}}}